package com.fasnote.alm.checklist.model;

import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 整个评审实例的状态枚举
 */
public enum ChecklistReviewStatus {
    PENDING("PENDING", "待开始"),
    IN_PROGRESS("IN_PROGRESS", "进行中"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELLED("CANCELLED", "已取消");
    
    private final String code;
    private final String description;
    
    ChecklistReviewStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    @JsonValue
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举值
     */
    public static ChecklistReviewStatus fromCode(String code) {
        for (ChecklistReviewStatus status : ChecklistReviewStatus.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown checklist review status code: " + code);
    }
    
    @Override
    public String toString() {
        return code;
    }
}
